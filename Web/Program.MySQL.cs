using FLEC.Model;
using FLEC.Model.Extensions;
using Microsoft.EntityFrameworkCore;

namespace Web;

public class Program
{
    public static async Task Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        // 配置服务
        ConfigureServices(builder.Services, builder.Configuration);

        var app = builder.Build();

        // 配置中间件管道
        await ConfigureMiddleware(app);

        app.Run();
    }

    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 添加 FLEC MySQL 数据库上下文
        services.AddFLECDbContextMySQL(configuration);

        // 或者使用连接池版本（推荐用于高并发场景）
        // services.AddFLECDbContextPoolMySQL(configuration, poolSize: 128);

        // 或者使用高级配置版本
        // services.AddFLECDbContextMySQLAdvanced(configuration, options =>
        // {
        //     // 自定义配置
        //     options.EnableServiceProviderCaching(false);
        // });

        // 添加控制器支持
        services.AddControllers();

        // 添加授权
        services.AddAuthorization();

        // 添加 Swagger/OpenAPI
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new() { Title = "FLEC API", Version = "v1" });
        });

        // 添加 CORS（如果需要）
        services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder.AllowAnyOrigin()
                       .AllowAnyMethod()
                       .AllowAnyHeader();
            });
        });

        // 添加健康检查
        services.AddHealthChecks();
    }

    /// <summary>
    /// 配置中间件管道
    /// </summary>
    /// <param name="app">应用程序</param>
    private static async Task ConfigureMiddleware(WebApplication app)
    {
        // 开发环境配置
        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "FLEC API V1");
                c.RoutePrefix = string.Empty; // 设置 Swagger UI 为根路径
            });

            // 自动应用数据库迁移（仅开发环境）
            await ApplyMigrations(app);
        }

        // 配置 HTTP 请求管道
        app.UseHttpsRedirection();

        // 启用 CORS
        app.UseCors();

        // 启用授权
        app.UseAuthorization();

        // 映射控制器
        app.MapControllers();

        // 添加健康检查端点
        app.MapHealthChecks("/health");

        // 示例 API 端点
        ConfigureApiEndpoints(app);
    }

    /// <summary>
    /// 配置 API 端点
    /// </summary>
    /// <param name="app">应用程序</param>
    private static void ConfigureApiEndpoints(WebApplication app)
    {
        var summaries = new[]
        {
            "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Balmy", "Hot", "Sweltering", "Scorching"
        };

        app.MapGet("/weatherforecast", (HttpContext httpContext) =>
            {
                var forecast = Enumerable.Range(1, 5).Select(index =>
                        new WeatherForecast
                        {
                            Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
                            TemperatureC = Random.Shared.Next(-20, 55),
                            Summary = summaries[Random.Shared.Next(summaries.Length)]
                        })
                    .ToArray();
                return forecast;
            })
            .WithName("GetWeatherForecast")
            .WithOpenApi();

        // 数据库测试端点
        app.MapGet("/api/test/database", async (FLECContextStandard context) =>
        {
            try
            {
                // 测试数据库连接
                var canConnect = await context.Database.CanConnectAsync();
                var connectionString = context.Database.GetConnectionString();
                
                return Results.Ok(new
                {
                    CanConnect = canConnect,
                    ConnectionString = connectionString?.Substring(0, Math.Min(50, connectionString.Length)) + "...",
                    DatabaseProvider = context.Database.ProviderName,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Database connection failed: {ex.Message}");
            }
        })
        .WithName("TestDatabaseConnection")
        .WithOpenApi();

        // 用户数量统计端点
        app.MapGet("/api/users/count", async (FLECContextStandard context) =>
        {
            try
            {
                var userCount = await context.Users.CountAsync();
                return Results.Ok(new { UserCount = userCount, Timestamp = DateTime.Now });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Failed to get user count: {ex.Message}");
            }
        })
        .WithName("GetUserCount")
        .WithOpenApi();
    }

    /// <summary>
    /// 应用数据库迁移
    /// </summary>
    /// <param name="app">应用程序</param>
    private static async Task ApplyMigrations(WebApplication app)
    {
        using var scope = app.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<FLECContextStandard>();
        
        try
        {
            // 检查是否有待应用的迁移
            var pendingMigrations = await context.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                Console.WriteLine($"Applying {pendingMigrations.Count()} pending migrations...");
                await context.Database.MigrateAsync();
                Console.WriteLine("Migrations applied successfully.");
            }
            else
            {
                Console.WriteLine("No pending migrations found.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Migration failed: {ex.Message}");
            // 在生产环境中，您可能希望记录错误而不是抛出异常
            // throw;
        }
    }
}
