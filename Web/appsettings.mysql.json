{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=FLEC;Uid=root;Pwd=password;CharSet=utf8mb4;", "MySQLConnection": "Server=localhost;Database=FLEC;Uid=root;Pwd=password;CharSet=utf8mb4;"}, "EntityFramework": {"EnableSensitiveDataLogging": false, "EnableDetailedErrors": false, "CommandTimeout": 30, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30", "QueryTrackingBehavior": "TrackAll", "QuerySplittingBehavior": "<PERSON><PERSON>uery"}, "Database": {"AutoMigrate": false, "SeedData": false, "ConnectionPoolSize": 128}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "AllowedHosts": "*"}