<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.13"/>
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5"/>
        <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.2"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5"/>
        <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.13"/>
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Model\Model.csproj" />
      <ProjectReference Include="..\Service\Service.csproj" />
    </ItemGroup>

</Project>
