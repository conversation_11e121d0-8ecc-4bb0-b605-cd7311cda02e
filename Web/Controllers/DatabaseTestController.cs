using FLEC.Model;
using FLEC.Model.DBModel;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Web.Controllers
{
    /// <summary>
    /// 数据库测试控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class DatabaseTestController : ControllerBase
    {
        private readonly FLECContextStandard _context;
        private readonly ILogger<DatabaseTestController> _logger;

        public DatabaseTestController(FLECContextStandard context, ILogger<DatabaseTestController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns>连接状态</returns>
        [HttpGet("connection")]
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                var canConnect = await _context.Database.CanConnectAsync();
                var connectionString = _context.Database.GetConnectionString();
                
                _logger.LogInformation("Database connection test: {CanConnect}", canConnect);
                
                return Ok(new
                {
                    CanConnect = canConnect,
                    ConnectionString = MaskConnectionString(connectionString),
                    DatabaseProvider = _context.Database.ProviderName,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database connection test failed");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 获取数据库信息
        /// </summary>
        /// <returns>数据库信息</returns>
        [HttpGet("info")]
        public async Task<IActionResult> GetDatabaseInfo()
        {
            try
            {
                var pendingMigrations = await _context.Database.GetPendingMigrationsAsync();
                var appliedMigrations = await _context.Database.GetAppliedMigrationsAsync();
                
                return Ok(new
                {
                    DatabaseProvider = _context.Database.ProviderName,
                    PendingMigrations = pendingMigrations.ToList(),
                    AppliedMigrations = appliedMigrations.ToList(),
                    PendingMigrationsCount = pendingMigrations.Count(),
                    AppliedMigrationsCount = appliedMigrations.Count(),
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get database info");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 获取表统计信息
        /// </summary>
        /// <returns>表统计信息</returns>
        [HttpGet("tables/stats")]
        public async Task<IActionResult> GetTableStats()
        {
            try
            {
                var stats = new
                {
                    Users = await _context.Users.CountAsync(),
                    Areas = await _context.Areas.CountAsync(),
                    Courses = await _context.Courses.CountAsync(),
                    Subjects = await _context.Subjects.CountAsync(),
                    Exams = await _context.Exams.CountAsync(),
                    Notices = await _context.Notices.CountAsync(),
                    Posts = await _context.Posts.CountAsync(),
                    Receipts = await _context.Receipts.CountAsync(),
                    Timestamp = DateTime.Now
                };

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get table stats");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 创建测试用户
        /// </summary>
        /// <returns>创建结果</returns>
        [HttpPost("test-user")]
        public async Task<IActionResult> CreateTestUser()
        {
            try
            {
                var testUser = new User
                {
                    id = Guid.NewGuid().ToString(),
                    // 根据您的 User 实体添加必要的属性
                    // username = "test_user_" + DateTime.Now.Ticks,
                    // email = $"test{DateTime.Now.Ticks}@example.com",
                    createTime = DateTime.Now,
                    updateTime = DateTime.Now
                };

                _context.Users.Add(testUser);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Test user created with ID: {UserId}", testUser.id);

                return Ok(new
                {
                    Message = "Test user created successfully",
                    UserId = testUser.id,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create test user");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 删除测试数据
        /// </summary>
        /// <returns>删除结果</returns>
        [HttpDelete("test-data")]
        public async Task<IActionResult> DeleteTestData()
        {
            try
            {
                // 删除测试用户（假设用户名包含 "test_user_"）
                var testUsers = await _context.Users
                    .Where(u => u.id.Contains("test") || (u.createTime.HasValue && u.createTime.Value > DateTime.Now.AddHours(-1)))
                    .ToListAsync();

                if (testUsers.Any())
                {
                    _context.Users.RemoveRange(testUsers);
                    await _context.SaveChangesAsync();
                }

                return Ok(new
                {
                    Message = $"Deleted {testUsers.Count} test users",
                    DeletedCount = testUsers.Count,
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete test data");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 执行原始 SQL 查询测试
        /// </summary>
        /// <returns>查询结果</returns>
        [HttpGet("raw-sql")]
        public async Task<IActionResult> TestRawSql()
        {
            try
            {
                // 执行简单的 SQL 查询
                var result = await _context.Database.SqlQueryRaw<int>("SELECT 1 as Value").ToListAsync();
                
                return Ok(new
                {
                    Message = "Raw SQL query executed successfully",
                    Result = result.FirstOrDefault(),
                    Timestamp = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Raw SQL query failed");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 掩码连接字符串敏感信息
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <returns>掩码后的连接字符串</returns>
        private static string? MaskConnectionString(string? connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return connectionString;

            // 简单的掩码处理，隐藏密码
            return connectionString.Length > 50 
                ? connectionString.Substring(0, 50) + "..." 
                : connectionString;
        }
    }
}
