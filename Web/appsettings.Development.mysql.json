{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=FLEC_Dev;Uid=root;Pwd=password;CharSet=utf8mb4;", "MySQLConnection": "Server=localhost;Database=FLEC_Dev;Uid=root;Pwd=password;CharSet=utf8mb4;"}, "EntityFramework": {"EnableSensitiveDataLogging": true, "EnableDetailedErrors": true, "CommandTimeout": 60, "MaxRetryCount": 5, "MaxRetryDelay": "00:01:00", "QueryTrackingBehavior": "TrackAll", "QuerySplittingBehavior": "<PERSON><PERSON>uery"}, "Database": {"AutoMigrate": true, "SeedData": true, "ConnectionPoolSize": 32}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "Microsoft.EntityFrameworkCore.Infrastructure": "Information"}}}