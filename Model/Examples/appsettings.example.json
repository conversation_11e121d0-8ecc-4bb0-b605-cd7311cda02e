{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=FLEC;Trusted_Connection=true;TrustServerCertificate=true;", "MySQLConnection": "Server=localhost;Database=FLEC;Uid=root;Pwd=password;", "PostgreSQLConnection": "Host=localhost;Database=FLEC;Username=postgres;Password=password;", "SQLiteConnection": "Data Source=FLEC.db"}, "EntityFramework": {"EnableSensitiveDataLogging": false, "EnableDetailedErrors": false, "CommandTimeout": 30, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30", "QueryTrackingBehavior": "TrackAll", "QuerySplittingBehavior": "<PERSON><PERSON>uery"}, "Database": {"AutoMigrate": false, "SeedData": false, "ConnectionPoolSize": 128}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}}