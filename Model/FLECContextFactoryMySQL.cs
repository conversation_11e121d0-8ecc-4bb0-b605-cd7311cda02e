using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using System.IO;

namespace FLEC.Model
{
    /// <summary>
    /// FLEC 数据库上下文工厂 - MySQL 版本
    /// </summary>
    public class FLECContextFactoryMySQL : IDesignTimeDbContextFactory<FLECContextStandard>
    {
        /// <summary>
        /// 创建数据库上下文实例
        /// </summary>
        /// <param name="args">命令行参数</param>
        /// <returns>数据库上下文实例</returns>
        public FLECContextStandard CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<FLECContextStandard>();

            // 获取连接字符串
            var connectionString = GetConnectionString();

            // 配置数据库提供程序 - MySQL
            optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), options =>
            {
                // MySQL 特定配置
                options.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorCodesToAdd: null);
                
                // 设置命令超时
                options.CommandTimeout(30);
                
                // 启用字符集配置
                options.CharSetBehavior(CharSetBehavior.NeverAppend);
                
                // 启用查询分割
                options.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
            });

            // 开发环境配置
            #if DEBUG
            optionsBuilder.EnableSensitiveDataLogging();
            optionsBuilder.EnableDetailedErrors();
            #endif

            return new FLECContextStandard(optionsBuilder.Options);
        }

        /// <summary>
        /// 获取连接字符串
        /// </summary>
        /// <returns>数据库连接字符串</returns>
        private static string GetConnectionString()
        {
            // 尝试从配置文件读取
            var configuration = BuildConfiguration();
            var connectionString = configuration.GetConnectionString("DefaultConnection") 
                                 ?? configuration.GetConnectionString("MySQLConnection");

            if (!string.IsNullOrEmpty(connectionString))
            {
                return connectionString;
            }

            // 如果配置文件中没有，使用默认 MySQL 连接字符串
            return "Server=localhost;Database=FLEC;Uid=root;Pwd=password;CharSet=utf8mb4;";
        }

        /// <summary>
        /// 构建配置
        /// </summary>
        /// <returns>配置实例</returns>
        private static IConfiguration BuildConfiguration()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.mysql.json", optional: true)
                .AddJsonFile("appsettings.Development.json", optional: true)
                .AddEnvironmentVariables();

            return builder.Build();
        }
    }
}
