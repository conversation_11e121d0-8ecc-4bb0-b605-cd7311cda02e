using System;
using FLEC.Model.DBModel;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FLEC.Model
{
    /// <summary>
    /// FLEC 数据库上下文 - EF 9.0 标准实现
    /// </summary>
    public class FLECContextStandard : DbContext
    {
        #region 构造函数
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="options">数据库上下文选项</param>
        public FLECContextStandard(DbContextOptions<FLECContextStandard> options) : base(options)
        {
            // 移除 Database.EnsureCreated() - 应该使用 Migrations
        }

        /// <summary>
        /// 无参构造函数 - 用于设计时工具
        /// </summary>
        public FLECContextStandard()
        {
        }

        #endregion

        #region DbSet 属性 - 使用 Pascal 命名规范

        /// <summary>
        /// 城市区域表
        /// </summary>
        public DbSet<Area> Areas { get; set; } = null!;

        /// <summary>
        /// 区域课程
        /// </summary>
        public DbSet<AreaCourse> AreaCourses { get; set; } = null!;

        /// <summary>
        /// 区域测试标准
        /// </summary>
        public DbSet<AreaTestStd> AreaTestStandards { get; set; } = null!;

        /// <summary>
        /// 首页轮播
        /// </summary>
        public DbSet<Carousel> Carousels { get; set; } = null!;

        /// <summary>
        /// 课程
        /// </summary>
        public DbSet<Course> Courses { get; set; } = null!;

        /// <summary>
        /// 课程视频
        /// </summary>
        public DbSet<CourseVideo> CourseVideos { get; set; } = null!;

        /// <summary>
        /// 课程标准
        /// </summary>
        public DbSet<CourseStd> CourseStandards { get; set; } = null!;

        /// <summary>
        /// 课程标准明细
        /// </summary>
        public DbSet<CourseStdDetail> CourseStandardDetails { get; set; } = null!;

        /// <summary>
        /// 扫码考试
        /// </summary>
        public DbSet<Exam> Exams { get; set; } = null!;

        /// <summary>
        /// 扫码考试记录明细
        /// </summary>
        public DbSet<ExamDetails> ExamDetails { get; set; } = null!;

        /// <summary>
        /// 扫码考试试题
        /// </summary>
        public DbSet<ExamSubject> ExamSubjects { get; set; } = null!;

        /// <summary>
        /// 扫码考试记录
        /// </summary>
        public DbSet<ExamUsers> ExamUsers { get; set; } = null!;

        /// <summary>
        /// 区域职务费用设置
        /// </summary>
        public DbSet<FeeSet> FeeSets { get; set; } = null!;

        /// <summary>
        /// 直播
        /// </summary>
        public DbSet<Live> Lives { get; set; } = null!;

        /// <summary>
        /// 新闻公告
        /// </summary>
        public DbSet<Notice> Notices { get; set; } = null!;

        /// <summary>
        /// 新闻公告区域关联表
        /// </summary>
        public DbSet<NoticeArea> NoticeAreas { get; set; } = null!;

        /// <summary>
        /// 职务
        /// </summary>
        public DbSet<Post> Posts { get; set; } = null!;

        /// <summary>
        /// 学校能学习的职务
        /// </summary>
        public DbSet<SchoolPost> SchoolPosts { get; set; } = null!;

        /// <summary>
        /// 收款明细
        /// </summary>
        public DbSet<Receipt> Receipts { get; set; } = null!;

        /// <summary>
        /// 学校扩展信息
        /// </summary>
        public DbSet<Schoolinfo> SchoolInfos { get; set; } = null!;

        /// <summary>
        /// 学校课程
        /// </summary>
        public DbSet<SchoolCourse> SchoolCourses { get; set; } = null!;

        /// <summary>
        /// 学校班级
        /// </summary>
        public DbSet<SchoolClass> SchoolClasses { get; set; } = null!;

        /// <summary>
        /// 题库
        /// </summary>
        public DbSet<Subject> Subjects { get; set; } = null!;

        /// <summary>
        /// 公用代码
        /// </summary>
        public DbSet<SysItems> SystemItems { get; set; } = null!;

        /// <summary>
        /// 系统参数
        /// </summary>
        public DbSet<SysParams> SystemParameters { get; set; } = null!;

        /// <summary>
        /// 用户
        /// </summary>
        public DbSet<User> Users { get; set; } = null!;

        /// <summary>
        /// 练习
        /// </summary>
        public DbSet<Practice> Practices { get; set; } = null!;

        /// <summary>
        /// 练习标准
        /// </summary>
        public DbSet<PracticeStd> PracticeStandards { get; set; } = null!;

        /// <summary>
        /// 课后习题
        /// </summary>
        public DbSet<VedioSubject> VideoSubjects { get; set; } = null!;

        /// <summary>
        /// 在线测试标准设置
        /// </summary>
        public DbSet<ExamSetting> ExamSettings { get; set; } = null!;

        /// <summary>
        /// 在线测试标准详细
        /// </summary>
        public DbSet<ExamSettingDetail> ExamSettingDetails { get; set; } = null!;

        /// <summary>
        /// 在线测试采用考试标准
        /// </summary>
        public DbSet<ExamSettingSchool> ExamSettingSchools { get; set; } = null!;

        /// <summary>
        /// 发证记录
        /// </summary>
        public DbSet<CertificateRecord> CertificateRecords { get; set; } = null!;

        /// <summary>
        /// 船只
        /// </summary>
        public DbSet<Boat> Boats { get; set; } = null!;

        /// <summary>
        /// 医院
        /// </summary>
        public DbSet<hospital> Hospitals { get; set; } = null!;

        /// <summary>
        /// 医生
        /// </summary>
        public DbSet<doctor> Doctors { get; set; } = null!;

        /// <summary>
        /// 通话记录
        /// </summary>
        public DbSet<callRecord> CallRecords { get; set; } = null!;

        /// <summary>
        /// 通话记录用户
        /// </summary>
        public DbSet<callRecordUser> CallRecordUsers { get; set; } = null!;

        /// <summary>
        /// 医生展示
        /// </summary>
        public DbSet<doctorShow> DoctorShows { get; set; } = null!;

        /// <summary>
        /// 应急知识
        /// </summary>
        public DbSet<EmergencyKnowledge> EmergencyKnowledges { get; set; } = null!;

        /// <summary>
        /// 用户推送
        /// </summary>
        public DbSet<userPush> UserPushes { get; set; } = null!;

        /// <summary>
        /// 船员
        /// </summary>
        public DbSet<Boater> Boaters { get; set; } = null!;

        /// <summary>
        /// 消息记录
        /// </summary>
        public DbSet<MsgRecord> MessageRecords { get; set; } = null!;

        // 数据传输相关实体
        public DbSet<DBModel.DataTransmission.StdBigRecord_> StandardBigRecords { get; set; } = null!;
        public DbSet<DBModel.DataTransmission.CourseInfo_> CourseInfos { get; set; } = null!;
        public DbSet<DBModel.DataTransmission.CourseVedioInfo_> CourseVideoInfos { get; set; } = null!;
        public DbSet<DBModel.DataTransmission.OnlineSubjects> OnlineSubjects { get; set; } = null!;
        public DbSet<DBModel.DataTransmission.vAnswers> VideoAnswers { get; set; } = null!;

        #endregion

        #region 模型配置

        /// <summary>
        /// 配置模型
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 应用所有配置
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(FLECContextStandard).Assembly);

            // 全局配置
            ConfigureGlobalSettings(modelBuilder);
            
            // 配置表名映射（保持与原数据库兼容）
            ConfigureTableNames(modelBuilder);
            
            // 配置索引
            ConfigureIndexes(modelBuilder);
            
            // 配置关系
            ConfigureRelationships(modelBuilder);
        }

        /// <summary>
        /// 配置全局设置
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        private static void ConfigureGlobalSettings(ModelBuilder modelBuilder)
        {
            // 设置默认字符串长度
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                foreach (var property in entityType.GetProperties())
                {
                    if (property.ClrType == typeof(string) && property.GetMaxLength() == null)
                    {
                        property.SetMaxLength(255);
                    }
                }
            }
        }

        /// <summary>
        /// 配置表名映射
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        private static void ConfigureTableNames(ModelBuilder modelBuilder)
        {
            // 保持与原数据库表名兼容
            modelBuilder.Entity<Area>().ToTable("area");
            modelBuilder.Entity<AreaCourse>().ToTable("area_course");
            modelBuilder.Entity<AreaTestStd>().ToTable("area_test_std");
            modelBuilder.Entity<Carousel>().ToTable("carousel");
            modelBuilder.Entity<Course>().ToTable("course");
            modelBuilder.Entity<CourseVideo>().ToTable("course_video");
            modelBuilder.Entity<CourseStd>().ToTable("course_std");
            modelBuilder.Entity<CourseStdDetail>().ToTable("course_std_detail");
            modelBuilder.Entity<Exam>().ToTable("exam");
            modelBuilder.Entity<ExamDetails>().ToTable("exam_details");
            modelBuilder.Entity<ExamSubject>().ToTable("exam_subject");
            modelBuilder.Entity<ExamUsers>().ToTable("exam_users");
            modelBuilder.Entity<FeeSet>().ToTable("fee_set");
            modelBuilder.Entity<Live>().ToTable("live");
            modelBuilder.Entity<Notice>().ToTable("notice");
            modelBuilder.Entity<NoticeArea>().ToTable("notice_area");
            modelBuilder.Entity<Post>().ToTable("post");
            modelBuilder.Entity<SchoolPost>().ToTable("school_post");
            modelBuilder.Entity<Receipt>().ToTable("receipt");
            modelBuilder.Entity<Schoolinfo>().ToTable("Schoolinfo");
            modelBuilder.Entity<SchoolCourse>().ToTable("school_course");
            modelBuilder.Entity<SchoolClass>().ToTable("SchoolClass");
            modelBuilder.Entity<Subject>().ToTable("subject");
            modelBuilder.Entity<SysItems>().ToTable("sys_Items");
            modelBuilder.Entity<SysParams>().ToTable("sys_params");
            modelBuilder.Entity<User>().ToTable("user");
            modelBuilder.Entity<Practice>().ToTable("practices");
            modelBuilder.Entity<PracticeStd>().ToTable("practice_std");
            modelBuilder.Entity<VedioSubject>().ToTable("vedio_subject");
            modelBuilder.Entity<ExamSetting>().ToTable("test_setting");
            modelBuilder.Entity<ExamSettingDetail>().ToTable("test_setting_detail");
            modelBuilder.Entity<ExamSettingSchool>().ToTable("test_setting_area");
            modelBuilder.Entity<CertificateRecord>().ToTable("certificate_record");
            modelBuilder.Entity<Boat>().ToTable("boat");
            modelBuilder.Entity<hospital>().ToTable("hospitals");
            modelBuilder.Entity<doctor>().ToTable("doctors");
            modelBuilder.Entity<callRecord>().ToTable("callRecords");
            modelBuilder.Entity<callRecordUser>().ToTable("callRecordUsers");
            modelBuilder.Entity<doctorShow>().ToTable("doctorShows");
            modelBuilder.Entity<EmergencyKnowledge>().ToTable("emergencyKnowledges");
            modelBuilder.Entity<userPush>().ToTable("userPushs");
            modelBuilder.Entity<Boater>().ToTable("boaters");
            modelBuilder.Entity<MsgRecord>().ToTable("msgRecords");
            
            // 数据传输表
            modelBuilder.Entity<DBModel.DataTransmission.StdBigRecord_>().ToTable("stdBigRecord");
            modelBuilder.Entity<DBModel.DataTransmission.CourseInfo_>().ToTable("CourseInfo");
            modelBuilder.Entity<DBModel.DataTransmission.CourseVedioInfo_>().ToTable("CourseVedioInfo");
            modelBuilder.Entity<DBModel.DataTransmission.OnlineSubjects>().ToTable("OnlineSubject");
            modelBuilder.Entity<DBModel.DataTransmission.vAnswers>().ToTable("vAnswer");
        }

        /// <summary>
        /// 配置索引
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        private static void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // 示例：为常用查询字段添加索引
            // modelBuilder.Entity<User>().HasIndex(u => u.Email).IsUnique();
            // modelBuilder.Entity<User>().HasIndex(u => u.Username).IsUnique();
        }

        /// <summary>
        /// 配置实体关系
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        private static void ConfigureRelationships(ModelBuilder modelBuilder)
        {
            // 示例：配置外键关系
            // modelBuilder.Entity<AreaCourse>()
            //     .HasOne<Area>()
            //     .WithMany()
            //     .HasForeignKey(ac => ac.AreaId);
        }

        #endregion

        #region 配置方法

        /// <summary>
        /// 配置数据库连接 - 用于设计时工具
        /// </summary>
        /// <param name="optionsBuilder">选项构建器</param>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // 仅在设计时使用，生产环境应通过依赖注入配置
                // optionsBuilder.UseSqlServer("Server=.;Database=FLEC;Trusted_Connection=true;");
            }

            // 启用敏感数据日志记录（仅在开发环境）
            #if DEBUG
            optionsBuilder.EnableSensitiveDataLogging();
            optionsBuilder.LogTo(Console.WriteLine, LogLevel.Information);
            #endif
        }

        #endregion

        #region 重写方法

        /// <summary>
        /// 保存更改前的处理
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>受影响的行数</returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // 自动设置审计字段
            SetAuditFields();
            
            return await base.SaveChangesAsync(cancellationToken);
        }

        /// <summary>
        /// 保存更改前的处理（同步版本）
        /// </summary>
        /// <returns>受影响的行数</returns>
        public override int SaveChanges()
        {
            // 自动设置审计字段
            SetAuditFields();
            
            return base.SaveChanges();
        }

        /// <summary>
        /// 设置审计字段
        /// </summary>
        private void SetAuditFields()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                // 如果实体有创建时间字段
                if (entry.Entity.GetType().GetProperty("CreateTime") != null)
                {
                    if (entry.State == EntityState.Added)
                    {
                        entry.Property("CreateTime").CurrentValue = DateTime.Now;
                    }
                }

                // 如果实体有更新时间字段
                if (entry.Entity.GetType().GetProperty("UpdateTime") != null)
                {
                    entry.Property("UpdateTime").CurrentValue = DateTime.Now;
                }
            }
        }

        #endregion
    }
}
