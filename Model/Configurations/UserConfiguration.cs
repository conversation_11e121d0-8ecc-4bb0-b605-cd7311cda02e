using FLEC.Model.DBModel;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FLEC.Model.Configurations
{
    /// <summary>
    /// 用户实体配置 - EF 9.0 最佳实践示例
    /// </summary>
    public class UserConfiguration : IEntityTypeConfiguration<User>
    {
        public void Configure(EntityTypeBuilder<User> builder)
        {
            // 表名配置
            builder.ToTable("user");

            // 主键配置
            builder.HasKey(u => u.id);

            // 属性配置
            builder.Property(u => u.id)
                .HasColumnName("id")
                .IsRequired();

            // 字符串属性配置
            if (HasProperty<User>("username"))
            {
                builder.Property("username")
                    .HasMaxLength(50)
                    .IsRequired();
            }

            if (HasProperty<User>("email"))
            {
                builder.Property("email")
                    .HasMaxLength(100);
            }

            if (HasProperty<User>("phone"))
            {
                builder.Property("phone")
                    .HasMaxLength(20);
            }

            // 索引配置
            if (HasProperty<User>("username"))
            {
                builder.HasIndex("username")
                    .IsUnique()
                    .HasDatabaseName("IX_User_Username");
            }

            if (HasProperty<User>("email"))
            {
                builder.HasIndex("email")
                    .HasDatabaseName("IX_User_Email");
            }

            // 值转换器示例（如果需要）
            // builder.Property(u => u.Status)
            //     .HasConversion<string>();

            // 查询过滤器（软删除）
            if (HasProperty<User>("IsDeleted"))
            {
                builder.HasQueryFilter(u => !EF.Property<bool>(u, "IsDeleted"));
            }
        }

        /// <summary>
        /// 检查实体是否有指定属性
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="propertyName">属性名</param>
        /// <returns>是否存在该属性</returns>
        private static bool HasProperty<T>(string propertyName)
        {
            return typeof(T).GetProperty(propertyName) != null;
        }
    }
}
