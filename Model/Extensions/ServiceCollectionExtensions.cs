using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace FLEC.Model.Extensions
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加 FLEC 数据库上下文
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <param name="connectionStringName">连接字符串名称</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddFLECDbContext(
            this IServiceCollection services,
            IConfiguration configuration,
            string connectionStringName = "DefaultConnection")
        {
            var connectionString = configuration.GetConnectionString(connectionStringName);
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException($"Connection string '{connectionStringName}' not found.");
            }

            services.AddDbContext<FLECContextStandard>(options =>
            {
                // 配置 SQL Server
                options.UseSqlServer(connectionString, sqlOptions =>
                {
                    // 启用重试机制
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorNumbersToAdd: null);

                    // 设置命令超时
                    sqlOptions.CommandTimeout(30);

                    // 启用分页查询优化
                    sqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                });

                // 配置日志记录
                options.LogTo(Console.WriteLine, LogLevel.Information);

                // 开发环境特定配置
                #if DEBUG
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
                #endif

                // 配置查询跟踪行为
                options.UseQueryTrackingBehavior(QueryTrackingBehavior.TrackAll);

                // 启用服务验证（开发环境）
                #if DEBUG
                options.EnableServiceProviderCaching(false);
                options.EnableSensitiveDataLogging();
                #endif
            });

            // 添加数据库健康检查
            services.AddHealthChecks()
                .AddDbContextCheck<FLECContextStandard>("database");

            return services;
        }

        /// <summary>
        /// 添加 FLEC 数据库上下文（使用 MySQL）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <param name="connectionStringName">连接字符串名称</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddFLECDbContextWithMySQL(
            this IServiceCollection services,
            IConfiguration configuration,
            string connectionStringName = "DefaultConnection")
        {
            var connectionString = configuration.GetConnectionString(connectionStringName);
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException($"Connection string '{connectionStringName}' not found.");
            }

            services.AddDbContext<FLECContextStandard>(options =>
            {
                // 配置 MySQL
                options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mySqlOptions =>
                {
                    // 启用重试机制
                    mySqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorCodesToAdd: null);

                    // 设置命令超时
                    mySqlOptions.CommandTimeout(30);
                });

                // 其他配置同上...
                #if DEBUG
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
                #endif
            });

            return services;
        }

        /// <summary>
        /// 添加 FLEC 数据库上下文（使用 PostgreSQL）
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <param name="connectionStringName">连接字符串名称</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddFLECDbContextWithPostgreSQL(
            this IServiceCollection services,
            IConfiguration configuration,
            string connectionStringName = "DefaultConnection")
        {
            var connectionString = configuration.GetConnectionString(connectionStringName);
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException($"Connection string '{connectionStringName}' not found.");
            }

            services.AddDbContext<FLECContextStandard>(options =>
            {
                // 配置 PostgreSQL
                options.UseNpgsql(connectionString, npgsqlOptions =>
                {
                    // 启用重试机制
                    npgsqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorCodesToRetryOn: null);

                    // 设置命令超时
                    npgsqlOptions.CommandTimeout(30);
                });

                // 其他配置同上...
                #if DEBUG
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
                #endif
            });

            return services;
        }

        /// <summary>
        /// 配置数据库连接池
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <param name="connectionStringName">连接字符串名称</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddFLECDbContextPool(
            this IServiceCollection services,
            IConfiguration configuration,
            string connectionStringName = "DefaultConnection")
        {
            var connectionString = configuration.GetConnectionString(connectionStringName);
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException($"Connection string '{connectionStringName}' not found.");
            }

            // 使用连接池可以提高性能
            services.AddDbContextPool<FLECContextStandard>(options =>
            {
                options.UseSqlServer(connectionString, sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorNumbersToAdd: null);
                });

                #if DEBUG
                options.EnableSensitiveDataLogging();
                #endif
            },
            poolSize: 128); // 连接池大小

            return services;
        }
    }
}
