using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace FLEC.Model.Extensions
{
    /// <summary>
    /// MySQL 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensionsMySQL
    {
        /// <summary>
        /// 添加 FLEC 数据库上下文 - MySQL 版本
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <param name="connectionStringName">连接字符串名称</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddFLECDbContextMySQL(
            this IServiceCollection services,
            IConfiguration configuration,
            string connectionStringName = "DefaultConnection")
        {
            var connectionString = configuration.GetConnectionString(connectionStringName)
                                 ?? configuration.GetConnectionString("MySQLConnection");
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException($"Connection string '{connectionStringName}' not found.");
            }

            services.AddDbContext<FLECContextStandard>(options =>
            {
                // 配置 MySQL
                options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mySqlOptions =>
                {
                    // 启用重试机制
                    mySqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorCodesToAdd: null);

                    // 设置命令超时
                    mySqlOptions.CommandTimeout(30);
                    
                    // 字符集配置
                    mySqlOptions.CharSetBehavior(CharSetBehavior.NeverAppend);
                    
                    // 启用查询分割
                    mySqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                });

                // 配置日志记录
                options.LogTo(Console.WriteLine, LogLevel.Information);

                // 开发环境特定配置
                #if DEBUG
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
                #endif

                // 配置查询跟踪行为
                options.UseQueryTrackingBehavior(QueryTrackingBehavior.TrackAll);
            });

            // 添加数据库健康检查
            services.AddHealthChecks()
                .AddDbContextCheck<FLECContextStandard>("mysql-database");

            return services;
        }

        /// <summary>
        /// 添加 FLEC 数据库上下文池 - MySQL 版本
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <param name="connectionStringName">连接字符串名称</param>
        /// <param name="poolSize">连接池大小</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddFLECDbContextPoolMySQL(
            this IServiceCollection services,
            IConfiguration configuration,
            string connectionStringName = "DefaultConnection",
            int poolSize = 128)
        {
            var connectionString = configuration.GetConnectionString(connectionStringName)
                                 ?? configuration.GetConnectionString("MySQLConnection");
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException($"Connection string '{connectionStringName}' not found.");
            }

            // 使用连接池可以提高性能
            services.AddDbContextPool<FLECContextStandard>(options =>
            {
                options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mySqlOptions =>
                {
                    mySqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorCodesToAdd: null);
                    
                    mySqlOptions.CommandTimeout(30);
                    mySqlOptions.CharSetBehavior(CharSetBehavior.NeverAppend);
                    mySqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                });

                #if DEBUG
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
                #endif
            },
            poolSize: poolSize);

            // 添加数据库健康检查
            services.AddHealthChecks()
                .AddDbContextCheck<FLECContextStandard>("mysql-database-pool");

            return services;
        }

        /// <summary>
        /// 添加 FLEC 数据库上下文 - 高级 MySQL 配置
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <param name="configureOptions">自定义配置选项</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddFLECDbContextMySQLAdvanced(
            this IServiceCollection services,
            IConfiguration configuration,
            Action<DbContextOptionsBuilder>? configureOptions = null)
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection")
                                 ?? configuration.GetConnectionString("MySQLConnection");
            
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("MySQL connection string not found.");
            }

            // 从配置读取 EF 设置
            var efConfig = configuration.GetSection("EntityFramework");
            var dbConfig = configuration.GetSection("Database");

            services.AddDbContext<FLECContextStandard>(options =>
            {
                // 基础 MySQL 配置
                options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), mySqlOptions =>
                {
                    var maxRetryCount = efConfig.GetValue<int>("MaxRetryCount", 3);
                    var maxRetryDelay = efConfig.GetValue<TimeSpan>("MaxRetryDelay", TimeSpan.FromSeconds(30));
                    var commandTimeout = efConfig.GetValue<int>("CommandTimeout", 30);

                    mySqlOptions.EnableRetryOnFailure(maxRetryCount, maxRetryDelay, null);
                    mySqlOptions.CommandTimeout(commandTimeout);
                    mySqlOptions.CharSetBehavior(CharSetBehavior.NeverAppend);
                    
                    // 查询分割配置
                    var querySplitting = efConfig.GetValue<string>("QuerySplittingBehavior", "SplitQuery");
                    if (Enum.TryParse<QuerySplittingBehavior>(querySplitting, out var splitBehavior))
                    {
                        mySqlOptions.UseQuerySplittingBehavior(splitBehavior);
                    }
                });

                // 查询跟踪配置
                var trackingBehavior = efConfig.GetValue<string>("QueryTrackingBehavior", "TrackAll");
                if (Enum.TryParse<QueryTrackingBehavior>(trackingBehavior, out var tracking))
                {
                    options.UseQueryTrackingBehavior(tracking);
                }

                // 开发环境配置
                var enableSensitiveLogging = efConfig.GetValue<bool>("EnableSensitiveDataLogging", false);
                var enableDetailedErrors = efConfig.GetValue<bool>("EnableDetailedErrors", false);

                if (enableSensitiveLogging)
                {
                    options.EnableSensitiveDataLogging();
                }

                if (enableDetailedErrors)
                {
                    options.EnableDetailedErrors();
                }

                // 自定义配置
                configureOptions?.Invoke(options);
            });

            return services;
        }
    }
}
